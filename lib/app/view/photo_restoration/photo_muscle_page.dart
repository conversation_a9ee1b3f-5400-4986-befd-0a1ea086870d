import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_muscle_provider.dart';
import 'package:text_generation_video/app/view/photo_restoration/widget/photo_muscle_case_list_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';
import 'package:text_generation_video/app/widgets/image_upload/upload_status_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

class PhotoMusclePage extends ConsumerWidget {
  const PhotoMusclePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var currentModification = ref.watch(photoModificationCurrentProvider);
    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          centerTitle: true,
          title: Text(
            "AI增肌",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
          toolbarHeight: 44.h,
          leading: const Leading(
            color: Colors.white,
          ),
          actions: [
            InkWell(
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                decoration: BoxDecoration(
                  color: const Color(0x30FFFFFF),
                  borderRadius: BorderRadius.circular(13),
                ),
                child: const Text(
                  "生成记录",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: Column(
          children: [
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(color: const Color(0xFF565656), width: 0.6),
              ),
              child: AspectRatio(
                aspectRatio: 1,
                child: UploadStatusWidget(
                  modification: currentModification,
                  defaultChild: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      ref
                          .read(photoModificationCurrentProvider.notifier)
                          .selectImg();
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(modificationSelectIcon, width: 26),
                        const SizedBox(height: 9),
                        const Text(
                          "添加图片",
                          style:
                              TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const Expanded(
                child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: PhotoMuscleCaseListWidget(),
            )),
            BottomActionBar(
              consumption: 20,
              onPress: () {
                ref.read(photoMuscleSelectCaseProvider.notifier).generateVideo();
              },
              buttonText: "生成视频",
            )
          ],
        ));
  }
}
