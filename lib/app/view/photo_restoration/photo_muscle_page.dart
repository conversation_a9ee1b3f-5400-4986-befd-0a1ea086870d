import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_muscle_provider.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_muscle.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';
import 'package:text_generation_video/app/widgets/image_upload/upload_status_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

class PhotoMusclePage extends ConsumerWidget {
  const PhotoMusclePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var currentModification = ref.watch(photoModificationCurrentProvider);

    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          centerTitle: true,
          title: Text(
            "AI增肌",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
          toolbarHeight: 44.h,
          leading: const Leading(
            color: Colors.white,
          ),
          actions: [
            InkWell(
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                decoration: BoxDecoration(
                  color: const Color(0x30FFFFFF),
                  borderRadius: BorderRadius.circular(13),
                ),
                child: const Text(
                  "生成记录",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: Column(
          children: [
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(color: const Color(0xFF565656), width: 0.6),
              ),
              child: AspectRatio(
                aspectRatio: 1,
                child: UploadStatusWidget(
                  modification: currentModification,
                  defaultChild: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      ref
                          .read(photoModificationCurrentProvider.notifier)
                          .selectImg();
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(modificationSelectIcon, width: 26),
                        const SizedBox(height: 9),
                        const Text(
                          "添加图片",
                          style:
                              TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Expanded(child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCaseListHeader(ref),
                  const SizedBox(height: 12),
                  Container(
                    color: Colors.red,
                    child: _buildCaseListContent(ref)
                    ),
                ],
              ),
            )),
            BottomActionBar(
              consumption: 20,
              onPress: () {
                ref.read(photoModificationCurrentProvider.notifier).commit();
              },
              buttonText: "生成视频",
            )
          ],
        ));
  }

  Widget _buildItemTab(String name) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
      child: Text(name, style: const TextStyle(fontSize: 14)),
    );
  }

  Widget _buildCaseListHeader(WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          "选择增肌强度",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFF565656), width: 0.6),
          ),
          child: DefaultTabController(
            length: 2,
            child: TabBar(
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              dividerHeight: 0,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: const Color(0xFF464548),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey,
              labelPadding: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              tabs: [_buildItemTab("男"), _buildItemTab("女")],
              onTap: (index) {
                ref.read(photoMuscleGenderProvider.notifier).setGender(index+1);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCaseListContent(WidgetRef ref) {
    var asyncCaseList = ref.watch(fetchPhotoMuscleCaseListProvider);
    return asyncCaseList.when(
      data: (caseList) {
        if (caseList == null || caseList.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: Text(
                "暂无模版数据",
                style: TextStyle(color: Colors.white60),
              ),
            ),
          );
        }
        return Expanded(
          child: ListView.builder(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            itemCount: caseList.length,
            itemBuilder: (context, index) {
              return _buildCaseItem(caseList[index], index == 0);
            },
          ),
        );
      },
      loading: () {
        return const Center(child: CircularProgressIndicator());
      },
      error: (err, stack) {
        return const Center(child: Text('无法加载历史记录'));
      },
    );
  }

  Widget _buildCaseItem(PhotoMuscleCaseItem caseItem, bool isSelected) {
    var itemWidth = 85.81.w;
    var itemHeight = 115.h;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isSelected ? const Color(0xFF30E6B8) : null,
          ),
          child: Container(
            margin: const EdgeInsets.all(2),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(isSelected ? 10 : 12),
              child: CachedNetworkImage(
                imageUrl: caseItem.caseImage ?? "",
                width: itemWidth,
                height: itemHeight,
                fit: BoxFit.cover,
                errorWidget: (_, o, s) {
                  return Container(
                    width: itemWidth,
                    height: itemHeight,
                    color: Colors.grey.shade800,
                  );
                },
              ),
            ),
          ),
        ),
        Container(
          width: itemWidth,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          margin: const EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withValues(alpha: 0.7),
              ],
            ),
          ),
          child: Text(
            caseItem.caseName ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 11,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
