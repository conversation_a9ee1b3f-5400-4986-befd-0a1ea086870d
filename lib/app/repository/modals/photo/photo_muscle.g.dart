// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_muscle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PhotoMuscleCaseItem _$PhotoMuscleCaseItemFromJson(Map<String, dynamic> json) =>
    PhotoMuscleCaseItem()
      ..caseHeight = (json['caseHeight'] as num?)?.toInt()
      ..caseId = (json['caseId'] as num?)?.toInt()
      ..caseImage = json['caseImage'] as String?
      ..casePrompt = json['casePrompt'] as String?
      ..caseName = json['caseName'] as String?
      ..caseWidth = (json['caseWidth'] as num?)?.toInt()
      ..id = (json['id'] as num?)?.toInt()
      ..sort = (json['sort'] as num?)?.toInt()
      ..state = (json['state'] as num?)?.toInt()
      ..gender = (json['gender'] as num?)?.toInt();

Map<String, dynamic> _$PhotoMuscleCaseItemToJson(
        PhotoMuscleCaseItem instance) =>
    <String, dynamic>{
      'caseHeight': instance.caseHeight,
      'caseId': instance.caseId,
      'caseImage': instance.caseImage,
      'casePrompt': instance.casePrompt,
      'caseName': instance.caseName,
      'caseWidth': instance.caseWidth,
      'id': instance.id,
      'sort': instance.sort,
      'state': instance.state,
      'gender': instance.gender,
    };
