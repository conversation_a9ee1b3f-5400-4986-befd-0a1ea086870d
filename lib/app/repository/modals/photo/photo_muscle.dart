import 'package:json_annotation/json_annotation.dart';

part 'photo_muscle.g.dart';

@JsonSerializable()
class PhotoMuscleCaseItem {
  int? caseHeight;
  int? caseId;
  String? caseImage;
  String? casePrompt;
  String? caseName;
  int? caseWidth;
  int? id;
  int? sort;
  int? state;
  int? gender;

  PhotoMuscleCaseItem();

  factory PhotoMuscleCaseItem.fromJson(Map<String, dynamic> json) =>
      _$PhotoMuscleCaseItemFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoMuscleCaseItemToJson(this);
}