import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_category.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_example.dart';
import 'package:text_generation_video/app/repository/service/same_style_service.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../navigation/router.dart';
import '../../repository/modals/same_style/same_style_banner.dart';

part 'same_style_provider.g.dart';

// 同款示例banner列表
@riverpod
Future<List<SameStyleBanner>?> fetchSameStyleBannerList(Ref ref) async {
  var result = await SameStyleService.sameExampleBanner();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

// 同款类型列表
@riverpod
class SameStyleCategoryList extends _$SameStyleCategoryList {
  @override
  List<SameCategory>? build() {
    getCategoryList();
    return null;
  }

  void getCategoryList() async {
    var result = await SameStyleService.sameCategoryList();
    if (result.status == Status.completed) {
      state = result.data;
      if (result.data != null && result.data!.isNotEmpty) {
        ref
            .read(currentSameStyleCategoryProvider.notifier)
            .setCategory(result.data?.first);
      }
    }
  }
}

/// 当前选中的同款分类类型
@riverpod
class CurrentSameStyleCategory extends _$CurrentSameStyleCategory {
  @override
  SameCategory? build() {
    return null;
  }

  void setCategory(SameCategory? sameCategory) async {
    state = sameCategory;
    ref.read(sameStyleExampleListProvider.notifier).loadData();
  }
}

/// 同款示例列表
/// 页长
const int pageSize = 10;

/// 同款示例列表结果
class SameStyleListResult {
  final int pageNo;
  final List<SameExample?>? exampleList;
  final LoadState? loadState;

  const SameStyleListResult({
    this.pageNo = 1,
    this.exampleList,
    this.loadState,
  });

  SameStyleListResult copyWith({
    int? page,
    List<SameExample?>? exampleList,
    LoadState? loadState,
  }) {
    return SameStyleListResult(
      pageNo: page ?? pageNo,
      exampleList: exampleList ?? this.exampleList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class SameStyleExampleList extends _$SameStyleExampleList {
  @override
  SameStyleListResult build() {
    state = const SameStyleListResult();
    return state;
  }

  /// 加载数据
  Future<void> loadData() async {
    var categoryType = ref.read(currentSameStyleCategoryProvider);
    if (categoryType == null || categoryType.categoryType == null) return;
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await SameStyleService.sameExampleList(
      categoryType.categoryType!,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      state = state.copyWith(
        exampleList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  Future<void> loadMore() async {
    var categoryType = ref.read(currentSameStyleCategoryProvider);
    if (categoryType == null || categoryType.categoryType == null) return;
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await SameStyleService.sameExampleList(
      categoryType.categoryType!,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          exampleList: [...?state.exampleList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

// 跳转指定功能页，可能会带默认参数
@riverpod
class SameStyleAction extends _$SameStyleAction {
  @override
  void build() {
    return;
  }

  void sameAction(int? functionType, {int? caseId}) {
    final funType = FunctionType.fromType(functionType);
    debugPrint("sameAction: ${funType?.label}");
    if (funType == null) return;

    // 人像写真
    if (funType.type == FunctionType.portraitPhoto.type && caseId != null) {
      // 案例详情页
      navigatorKey.currentContext?.push("/$photoPortraitDetailPage", extra: caseId);
      return;
    }

    navigatorKey.currentContext?.push("/${funType.label}", extra: caseId);
  }
}

// 跳转功能枚举
enum FunctionType {
  //"文生视频"
  textToVideo(1, textToVideoPage),

  //"首帧图片生视频"
  imageToVideo(2, "首帧图片生视频"),

  //"老照片修复"
  oldPhotoRepair(3, oldPhotoRestorationPage),

  //"画质变清晰"
  qualityEnhance(4, qualityRestorationPage),

  //"智能抠图"
  smartMatting(5, photoMattingPage),

  //"创意特效视频"
  creativeEffectVideo(6, "创意特效视频"),

  //"照片跳舞视频"
  photoDance(7, "照片跳舞视频"),

  //"AI绘图"
  aiDrawing(8, "AI绘图"),

  //"AI改图"
  aiEditImage(9, photoModificationPage),

  //"人像写真"
  portraitPhoto(10, photoPortraitPage),

  //"服装商拍"
  clothingShoot(11, "服装商拍"),

  //"融合生视频"
  fusionVideo(12, "融合生视频"),

  // "照片增肌"
  muscleEnhance(13, photoMusclePage),

  // "对口型唱歌"
  lipSync(14, syncSingPage),

  // "商品图包装"
  productPackaging(15, "商品图包装"),

  // "卡通数字人"
  cartoonDigitalHuman(16, cartoonDigimonPage),

  // "宠物唱歌"
  petSinging(17, petSingPage),

  // "海报设计"
  posterDesign(18, "海报设计"),

  // "口播数字人"
  digitalHumanSpeech(19, digitalHumanWidget),

  // "首尾帧生视频"
  frameGenerationVideo(20, "首尾帧生视频");

  final int type;
  final String label;

  const FunctionType(this.type, this.label);

  /// 根据 type 获取枚举
  static FunctionType? fromType(int? type) {
    try {
      return FunctionType.values.firstWhere((e) => e.type == type);
    } catch (_) {
      return null;
    }
  }
}
