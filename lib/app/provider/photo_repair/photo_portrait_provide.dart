import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

part 'photo_portrait_provide.g.dart';

@riverpod
Future<List<PhotoPortraitBanner>?> fetchPhotoPortraitBanner(Ref ref) async {
  var result = await TextToVideoService.photoPortraitBanner();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

@riverpod
Future<List<PhotoPortraitCategory>?> fetchphotoPortraitCategoryList(
    Ref ref) async {
  var result = await TextToVideoService.photoPortraitCategory();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}
@riverpod
Future<PhotoPortraitCategoryDetail?> fetchphotoPortraitDetail(Ref ref, int caseId) async {
  var result = await TextToVideoService.photoPortraitDetail(caseId);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}


@riverpod
class PhotoPortraitUploadImage extends _$PhotoPortraitUploadImage {
  @override
  String? build() {
    ref.listen(photoModificationCurrentProvider, (previous, next) {
      if (next.state == 2 && next.remoteUrl != null) {
        // 上传成功时，更新当前图片URL并添加到历史记录
        state = next.remoteUrl;
        ref.read(photoHistoryProvider.notifier).addImage(next.remoteUrl!);
        debugPrint("图片上传成功，已添加到历史记录: ${next.remoteUrl}");
      } else if (next.state == 3) {
        // 上传失败
        debugPrint("图片上传失败");
      } else if (next.state == 1) {
        // 上传中
        debugPrint("图片上传中...");
      }
    });
    return null;
  }
  /// 开始改图
  void commit(String prompt) async {
    var imgUrl = state;
    if (imgUrl == null) {
      if(ref.read(photoHistoryProvider).value?.isEmpty == true){
        ToastUtil.showToast("请先上传图片");
      }else{
        ToastUtil.showToast("请先选择图片");
        return;
      }
      return;
    }

    if (prompt.isEmpty) {
      ToastUtil.showToast("请输入提示内容");
      return;
    }
    SmartDialog.showLoading(msg: "提交中...");
    var result = await TextToVideoService.imageToImage(prompt, imgUrl);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ToastUtil.showToast("提交成功");
    } else {
      ToastUtil.showToast("提交失败");
      print(result.exception!.getMessage());
    }
  }

  void setImage() {
    ref.read(photoModificationCurrentProvider.notifier).selectImg();
  }

  void setCurrentImageUrl(String? url) {
    state = url;
  }

  void removeImage(String url) {
    ref.read(photoHistoryProvider.notifier).removeImage(url);
    state = null;
  }
}

const _photoHistoryKey = 'photo_history';

@riverpod
class PhotoHistory extends _$PhotoHistory {
  @override
  Future<List<String>> build() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_photoHistoryKey) ?? [];
  }

  Future<void> addImage(String url) async {
    final prefs = await SharedPreferences.getInstance();
    final history = state.value ?? [];
    if (!history.contains(url)) {
      final newHistory = [url, ...history];
      await prefs.setStringList(_photoHistoryKey, newHistory);
      state = AsyncValue.data(newHistory);
    }
  }

  Future<void> removeImage(String url) async {
    final prefs = await SharedPreferences.getInstance();
    final history = state.value ?? [];
    if (history.remove(url)) {
      await prefs.setStringList(_photoHistoryKey, history);
      state = AsyncValue.data(List.from(history));
    }
  }
}