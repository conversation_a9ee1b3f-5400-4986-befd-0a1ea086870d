// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_muscle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchPhotoMuscleCaseListHash() =>
    r'29d963fb543a20edbef1440f6139256fcc78848f';

/// See also [fetchPhotoMuscleCaseList].
@ProviderFor(fetchPhotoMuscleCaseList)
final fetchPhotoMuscleCaseListProvider =
    AutoDisposeFutureProvider<List<PhotoMuscleCaseItem>?>.internal(
  fetchPhotoMuscleCaseList,
  name: r'fetchPhotoMuscleCaseListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchPhotoMuscleCaseListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchPhotoMuscleCaseListRef
    = AutoDisposeFutureProviderRef<List<PhotoMuscleCaseItem>?>;
String _$photoMuscleGenderHash() => r'a059f8de9f8f4a3b70bdd38a657a1ce2c6e778ef';

/// See also [PhotoMuscleGender].
@ProviderFor(PhotoMuscleGender)
final photoMuscleGenderProvider =
    AutoDisposeNotifierProvider<PhotoMuscleGender, int>.internal(
  PhotoMuscleGender.new,
  name: r'photoMuscleGenderProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photoMuscleGenderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoMuscleGender = AutoDisposeNotifier<int>;
String _$photoMuscleSelectCaseHash() =>
    r'dc67187de179ee11660e9fe0fb1828ccc8b979c1';

/// See also [PhotoMuscleSelectCase].
@ProviderFor(PhotoMuscleSelectCase)
final photoMuscleSelectCaseProvider = AutoDisposeNotifierProvider<
    PhotoMuscleSelectCase, PhotoMuscleCaseItem>.internal(
  PhotoMuscleSelectCase.new,
  name: r'photoMuscleSelectCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photoMuscleSelectCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoMuscleSelectCase = AutoDisposeNotifier<PhotoMuscleCaseItem>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
