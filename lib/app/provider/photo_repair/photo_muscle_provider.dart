
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_muscle.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

part 'photo_muscle_provider.g.dart';

@riverpod
Future<List<PhotoMuscleCaseItem>?> fetchPhotoMuscleCaseList(Ref ref) async {
  var gender = ref.watch(photoMuscleGenderProvider);
  var result = await TextToVideoService.photoMuscleCaseList(gender);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

@riverpod
class PhotoMuscleGender extends _$PhotoMuscleGender {
  @override
  int build() {
    return 1;
  }

  void setGender(int gender) {
    state = gender;
  }
}

@riverpod
class PhotoMuscleSelectCase extends _$PhotoMuscleSelectCase {
  @override
  PhotoMuscleCaseItem build() {
    return PhotoMuscleCaseItem();
  }
  
  void setCaseItem(PhotoMuscleCaseItem caseItem) {
    state = caseItem;
  }

  void generateVideo() async {
    var imgUrl = ref.read(photoModificationCurrentProvider).remoteUrl;
    if (imgUrl == null) {
        ToastUtil.showToast("请先选择图片");
        return;
    }

    if (state.casePrompt == null) {
      ToastUtil.showToast("请先选择模版");
      return;
    }
    SmartDialog.showLoading(msg: "提交中...");
    var result = await TextToVideoService.imageToImage(state.casePrompt!, imgUrl);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ToastUtil.showToast("提交成功");
    } else {
      ToastUtil.showToast("提交失败");
      print(result.exception!.getMessage());
    }
  }
}