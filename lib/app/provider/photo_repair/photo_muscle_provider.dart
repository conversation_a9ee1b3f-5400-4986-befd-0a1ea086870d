
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_muscle.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';

part 'photo_muscle_provider.g.dart';

@riverpod
Future<List<PhotoMuscleCaseItem>?> fetchPhotoMuscleCaseList(Ref ref) async {
  var gender = ref.watch(photoMuscleGenderProvider);
  var result = await TextToVideoService.photoMuscleCaseList(gender);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

@riverpod
class PhotoMuscleGender extends _$PhotoMuscleGender {
  @override
  int build() {
    return 1;
  }

  void setGender(int gender) {
    state = gender;
  }
}